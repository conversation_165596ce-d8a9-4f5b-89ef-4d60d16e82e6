/** 配置表 */
export interface systemConfigTable {
  _id: string;
  select_topic_number: { label: string; value: number }[];
  prompt_template: { [key: string]: any }[];
}

interface CommonTemplate {
  _id?: string;
  uuid?: string;
  open_id: string;

  create_at?: string;
  update_at?: string;
  delete_at?: string | null;
}
interface MediaTemplate {
  oss_url: string;
  oss_path: string;
  format: string;
}
/** 用户信息表 */
export interface UserTable extends CommonTemplate {
  name: string;
  password: string;
  role: string;
  tokens: number;
}
/** 音频表 */
export interface AudioTable extends MediaTemplate, CommonTemplate {
  title: string;
  description: string;
  duration: number;
  model_id: string;
  model_status: string;
}
/** 视频表 */
export interface VideoTable extends MediaTemplate {
  title: string;
  description: string;
  duration: number;
  model_id: string | null;
  /** 音频模型状态 */
  model_status: string;
  /** 封面 */
  cover: string;
}
/** 文章表 */
export interface ArticleTable extends CommonTemplate {
  /** 用户填充信息 */
  input_value: string;
  /** 配置信息 */
  params: { [key: string]: any };
  /** 大模型输出结果 */
  LLM_result: string;
  /** 解析结果、主要针对是选题 */
  parse_result: any;
  /** 思考过程 */
  thinking: string;
  /** 文案类型 */
  type: "selectTopic" | "article" | "polish";
  /** 消耗多少点数 */
  token: number;
}
interface BlockTemplate {
  state: boolean;
  data: any[] | string;
  stream: string;
  editor: boolean;
}
const progress = {
  1: "topic", // 1. 选题
  2: "createwriting", // 2. 创建文案
  3: "selectAudio", // 3. 选择音频
  4: "createAudio", // 4. 生成音频
  5: "selectvideo", // 5. 选择视频
  6: "createVideo", // 6. 生成视频
};
/** 创作流表 */
export interface WorkflowTable extends CommonTemplate {
  type: "selected_topic" | "only_topic" | "copywriting";
  process: number;
  result: {
    selectTitle?: BlockTemplate & { [key: string]: any };
    createwriting: BlockTemplate & { [key: string]: any };
    selectAudio: BlockTemplate & { [key: string]: any };
    createAudio: BlockTemplate & { [key: string]: any };
    selectvideo: BlockTemplate & { [key: string]: any };
    createVideo: BlockTemplate & { [key: string]: any };
  };
}

/** IP 人设标 */
export interface IPSettingTable extends CommonTemplate {
  ip_name: string;
  sex: "man" | "woman";
  create_type: any;
  target_audience: any;
  key_information: string;
  track_position: string;
}
