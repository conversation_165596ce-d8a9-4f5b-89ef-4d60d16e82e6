import { insertWorkflowOne, getWorkflowById } from "../../models/workflow";
/** 选题模板 */
const selectTitleTemplate = {
  state: false,
  data: "",
  stream: "",
  editor: false,
};
/** 文案模板 */
const createwritingTemplate = {
  state: false,
  data: "",
  stream: "",
  editor: false,
};
/** 音频模板 */
const selectAudioTemplate = {
  state: false,
  data: "",
  stream: "",
  editor: false,
};
/** 生成音频模板 */
const createAudioTemplate = {
  state: false,
  data: "",
  stream: "",
  editor: false,
};
/** 视频模板 */
const selectvideoTemplate = {
  state: false,
  data: "",
  stream: "",
  editor: false,
};
/** 生成视频模板 */
const createVideoTemplate = {
  state: false,
  data: "",
  stream: "",
  editor: false,
};
const TEMPLATE = {
  // 创建选题
  selected_topic: {
    process: 1,
    result: {
      selectTitle: selectTitleTemplate,
      createwriting: createwritingTemplate,
      selectAudio: selectAudioTemplate,
      createAudio: createAudioTemplate,
      selectvideo: selectvideoTemplate,
      createVideo: createVideoTemplate,
    },
  },
  // 定义选题
  only_topic: {
    process: 2,
    result: {
      selectTitle: selectTitleTemplate,
      createwriting: createwritingTemplate,
      selectAudio: selectAudioTemplate,
      createAudio: createAudioTemplate,
      selectvideo: selectvideoTemplate,
      createVideo: createVideoTemplate,
    },
  },
  // 无选题
  copywriting: {
    process: 3,
    result: {
      createwriting: createwritingTemplate,
      selectAudio: selectAudioTemplate,
      createAudio: createAudioTemplate,
      selectvideo: selectvideoTemplate,
      createVideo: createVideoTemplate,
    },
  },
};
type WorkflowType = "selected_topic" | "only_topic" | "copywriting";

interface CreateWorkflowParams {
  open_id: string;
  type: WorkflowType;
}
/** 创建工作流基础数据 */
const createWorkflow = async ({
  open_id,
  type,
}: CreateWorkflowParams): Promise<any> => {
  const template = TEMPLATE[type];
  if (!template) {
    throw new Error(`Invalid workflow type: ${type}`);
  }
  try {
    // 初始化任务
    const initData = await insertWorkflowOne({
      open_id,
      type,
      process: template.process,
      result: template.result,
    });
    // 查询任务详情数据
    return await getWorkflowById(initData.uuid);
  } catch (error) {
    // 可根据实际需求记录日志
    throw new Error(`Failed to create workflow: ${(error as Error).message}`);
  }
};
interface ContinueType {
  uuid: string;
}
/** 工作流继续接口 */
const continueWorkflow = async (params: ContinueType) => {
  try {
    // 查询任务详情数据
    return await getWorkflowById(params.uuid);
  } catch (error) {
    // 可根据实际需求记录日志
    throw new Error(`Failed to continue workflow: ${(error as Error).message}`);
  }
};
export { createWorkflow, continueWorkflow };
