import { FastifyRequest } from "fastify";
import fs from "fs/promises";
const FISH_API_KEY = "80ace87e18d0489da6f96f33d51f8023";
const FISH_API_URL = "https://hfw-api.hifly.cc";

interface CreateModelTaskOptions {
  audio_url: string[];
  title: string;
  request: FastifyRequest;
}

/**
 * 创建声音模型任务
 * @ param modelId
 * @param options
 */
const HiflyCreateAudioModelTask = async ({
  audio_url,
  title,
  request,
}: CreateModelTaskOptions) => {
  const stats = await fs.readFile(audio_url[0]);
  request.log.info(`获取音频数据:${stats}`);
  const formData = new FormData();
  formData.append("file_id", new Blob([stats]));
  formData.append("voice_type", "8");
  formData.append("title", title.substring(0, 19));
  request.log.info(`${FISH_API_URL}/api/v2/hifly/voice/create`);
  return new Promise((resolve, reject) => {
    fetch(`${FISH_API_URL}/api/v2/hifly/voice/create`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${FISH_API_KEY}`,
      },
      body: formData,
    })
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        console.log("创建模型:", data);
        if (data.code === 200) {
          resolve({
            model_id: data.task_id,
            title: title,
          });
        } else {
          reject({
            title: title,
            message: data.message,
            code: data.code,
          });
        }
      })
      .catch((error) => {
        console.log("失败", error);
        reject(error);
      });
  });
};
interface HiflyCreateByTts {
  voice: string;
  text: string;
  title?: string;
}
/**
 * 文字创作音频创作
 * @param {
 *  voice: string;
 *  text: string;
 *  title?: string;
 * }
 */
const HiflyCreateByTts = async ({
  voice,
  text,
  title,
}: HiflyCreateByTts): Promise<{
  status: number;
  code: number;
  message: string;
  demo_url: string;
  request_id: string;
}> => {
  return new Promise((resolve, reject) => {
    fetch(`${FISH_API_URL}/api/v2/hifly/audio/create_by_tts`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${FISH_API_KEY}`,
      },
      body: JSON.stringify({
        voice,
        text,
        title,
      }),
    })
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        console.log("创建模型:", data);
        if (data.code === 200) {
          resolve({
            status: data.status,
            code: data.code,
            message: data.message,
            demo_url: data.demo_url,
            request_id: data.request_id,
          });
        } else {
          resolve({
            status: data.status,
            code: data.code,
            message: data.message,
            demo_url: data.demo_url,
            request_id: data.request_id,
          });
        }
      })
      .catch((error) => {
        console.log("失败", error);
        reject(error);
      });
  });
};

/**
 * 查询创作任务状态
 * @params{
 *   task_id:string
 * }
 */
const HiflyVoiceTask = async (taskId: string) => {
  return new Promise((resolve, reject) => {
    fetch(`${FISH_API_URL}/api/v2/hifly/voice/task?task_id=${taskId}`, {
      headers: {
        Authorization: `Bearer ${FISH_API_KEY}`,
      },
    })
      .then((response) => response.json())
      .then((data) => {
        console.log("创建模型:", data);
        if (data?.code === 0) {
          resolve({
            status: data.status,
            voice: data.voice,
            demo_url: data.demo_url,
            request_id: data.request_id,
          });
        } else {
          reject({
            message: data.message,
            code: data.code,
          });
        }
      })
      .catch((error) => {
        console.log("失败", error);
        reject(error);
      });
  });
};
export { HiflyCreateAudioModelTask, HiflyCreateByTts, HiflyVoiceTask };
