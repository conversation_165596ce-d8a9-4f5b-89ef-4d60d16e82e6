import { MultipartFile } from "@fastify/multipart";
import pump from "pump";
import fs from "fs";
import {
  generateSafeFilename,
  ensureUploadDir,
  mediaInfo,
  extractVideoThumbnail,
} from "../common";
import { VideoConfig } from "../config";
import { insertVideoOne } from "../../models/video";
import { CreateVideoModel } from "./Feiying";
import { uploadLocalFileToOSS, uploadFileToOSS } from "../../oss/index";
import path from "path";

export const createVideoModel = async (
  file: MultipartFile
): Promise<{
  code: number;
  message?: string;
  filename?: string;
  mimetype?: string;
  oss?: string;
  file_id?: string;
  [key: string]: any;
}> => {
  try {
    const { filename, mimetype, fields } = file;
    // 获取文件的参数
    const filesParams: { [key: string]: any } = {};
    const arr = Object.entries(fields);
    arr.forEach(([key, value]: [key: string, value: any], index) => {
      if (value.type !== "file") {
        filesParams[key] = value.value;
      }
    });
    console.log("视频文件类型:", mimetype);
    // 验证文件类型
    if (!VideoConfig.allowedMimeTypes.includes(mimetype)) {
      return {
        code: 500,
        message: "文件类型不支持",
      };
    }
    // 生成安全文件名
    const safeFilename = await generateSafeFilename(filename);
    // 保存文件
    await ensureUploadDir(`${VideoConfig.uploadDir}`);
    // 临时文件路径
    const tempFilePath = path.join(VideoConfig.uploadDir, safeFilename);

    // 保存文件到本地
    await new Promise<void>((resolve, reject) => {
      pump(file.file, fs.createWriteStream(tempFilePath), (err) => {
        if (err) {
          reject(err.message);
          return {
            code: 500,
            message: "保存本地失败",
          };
        }
        resolve();
      });
    });
    const mediaData = await mediaInfo(tempFilePath);
    // const mdeiaPic = await extractVideoThumbnail({
    //   videoPath: tempFilePath,
    //   outputDir: VideoConfig.uploadDir,
    // });

    // 上传文件到oss
    const ossResult = await uploadLocalFileToOSS(tempFilePath, "video");
    if (!ossResult.url) {
      return {
        code: 500,
        message: "上传oss失败",
      };
    }
    // 上传视频封面到OSS
    // const ossPicResult = await uploadLocalFileToOSS(mdeiaPic, "video");
    // if (!ossPicResult.url) {
    //   return {
    //     code: 500,
    //     message: "上传oss失败",
    //   };
    // }
    // fs.unlinkSync(tempFilePath);
    // // 创建数字人模型
    // const result = await CreateVideoModel({
    //   title: "测试",
    //   video_url: ossResult.url,
    // });

    // 将视频信息存入数据库
    await insertVideoOne({
      open_id: filesParams.open_id || "admin",
      title: filesParams.title || "测试视频",
      description: filesParams.description || "测试视频描述",
      oss_url: ossResult.url,
      oss_path: ossResult.ossPath || "",
      format: mimetype,
      duration: mediaData.duration,
      model_id: "", //params.model_id,
      model_status: "", //params.model_status,
      cover: "", //ossPicResult.url,
    });
    return {
      code: 200,
      filename: `${safeFilename}`,
      mimetype,
      oss: ossResult.url,
      cover: "", //ossPicResult.url,
      file_id: "",
    };
  } catch (error: any) {
    return {
      code: 500,
      message: error.message || "上传失败",
    };
  }
};
