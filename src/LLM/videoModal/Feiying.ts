import fs from "fs/promises";

const HFW_API_KEY = "80ace87e18d0489da6f96f33d51f8023";
const HFW_API_URL = "https://hfw-api.hifly.cc";
interface CreateModelTaskOptions {
  title: string;
  video_url: string;
}
/**
 * 创建数字人模型
 * @params
 */
const CreateVideoModel = async ({
  //   paths,
  title,
  video_url,
}: CreateModelTaskOptions): Promise<{
  message: string;
  code: number;
  task_id: string;
  request_id: string;
}> => {
  return new Promise((resolve, reject) => {
    fetch(`${HFW_API_URL}/api/v2/hifly/avatar/create_by_video`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${HFW_API_KEY}`,
      },
      body: JSON.stringify({
        title,
        video_url,
      }),
    })
      .then((response) => {
        return response.json();
      })
      .then((data) => {
        console.log("创建模型:", data);
        if (data.state !== "failed") {
          resolve(data);
        } else {
          reject(data);
        }
      })
      .catch((error) => {
        console.log("失败", error);
        reject(error);
      });
  });
};
export { CreateVideoModel };
