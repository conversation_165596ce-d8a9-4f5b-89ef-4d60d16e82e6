const WriterExpert = ({
  content,
  length = 500,
}: {
  content: string;
  length?: number;
}) => {
  return `
    请根据以下输入内容生成一篇口播稿：
    - 类型：口播稿、文案
    - 语言风格：简洁明了, 口语化
    - 目标受众：""
    - 关键信息必须包含：""
    - 要求：
      1. 只输出文案，其他的部分不要输出
      2. 文案长度控制在${length}字左右
      3. 文案中不要出现文案总结的标题
      4. 文案中不要出现“口播稿”字样
      5. 文案中不要出现使用小括号描述 语气、动作、状态的信息

    输入内容：[${content}]
  `;
  // prompt: {
  //   //基础信息层
  //   task: "", //明确任务类型
  //   // 包含请求元数据
  //   // metadata: {
  //   //   api_version: "v1.2",
  //   //   request_id: "voice_script_12345",
  //   //   timestamp: "2023-11-15T14:30:00Z",
  //   // },
  //   // 内容要求层
  //   content_requirements: {
  //     // 指定口播稿类型
  //     script_type: "口播稿",
  //     // 主题
  //     theme: "",
  //     // 口播稿风格
  //     language_style: {},
  //     tone: {
  //       style: "自然流畅",
  //       emotion: "富有节奏感",
  //       avoid: ["简洁明了", "重点突出", "易于理解", "适合朗读"],
  //     },
  //     // 口播稿结构、结构化内容框架
  //     structure: {
  //       // duration: "1分30秒",
  //       // word_count: "约450字",
  //       // sections: [
  //       //   {
  //       //     name: "开场白",
  //       //     purpose: "吸引注意力",
  //       //     required_element: "提出问题引发好奇",
  //       //   },
  //       //   {
  //       //     name: "产品介绍",
  //       //     purpose: "核心功能展示",
  //       //     required_elements: ["三大核心功能", "技术优势"],
  //       //   },
  //       //   {
  //       //     name: "行动号召",
  //       //     purpose: "促成转化",
  //       //     required_elements: ["限时优惠", "购买渠道"],
  //       //   },
  //       // ],
  //     },
  //     // 受众定位层
  //     audience: {
  //       // age_range: "25-45岁",
  //       // characteristics: ["科技爱好者", "家居改善需求者"],
  //     },
  //     // 个性化要求层
  //     personalization: {
  //       // rhetorical_devices: ["排比句", "设问句"],
  //       // verbal_cues: ["强调处添加停顿标记(//)"],
  //     },
  //     // 特殊要求层
  //     special_requirements: {
  //       // rhetorical_devices: ["排比句", "设问句"],
  //       // verbal_cues: ["强调处添加停顿标记(//)"],
  //       // inclusions: ["品牌口号: '科技改变生活'"],
  //       // exclusions: ["竞争对手直接比较"],
  //     },
  //   },
  //   // 返回结果的格式要求
  //   output_format: {
  //     language: "简体中文",
  //     format: "纯文本",
  //     annotations: {
  //       pause_marks: true,
  //       emphasis_marks: true,
  //     },
  //   },
  //   rules: [
  //     "**输出格式**：先输出你的思考过程，再输出最终口播稿",
  //     "**思维链**：所有思维链内容必须用 `<think>...</think>` 标签包裹",
  //     "**口播稿**：最终内容用是文本格式，不需要任何标签，不要括号状态",
  //   ],
  //   examples: `
  //     <think>
  //       - 用户要的是口播稿，需要先理清逻辑
  //       - 主题是AI未来趋势，可以分3个方向：技术突破、行业应用、社会影响
  //       - 避免太专业术语，要通俗易懂
  //     </think>
  //     大家好，今天我们来聊聊AI的未来趋势...
  //   `,
  //   // 质量控制层、明确质量检查点、修订指导
  //   quality_control: {
  //     review_points: ["技术术语准确性", "情感传递有效性", "行动号召力强度"],
  //     revision_instructions: "如果第一版科技感不足，请加强专业术语使用",
  //   },
  // },
};
export default WriterExpert;
