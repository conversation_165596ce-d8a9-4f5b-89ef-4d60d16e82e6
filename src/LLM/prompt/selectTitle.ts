const SelectTitle = ({ content }: { content: { [key: string]: any } }) => {
  return `
      请根据输入内容创建${content.number ?? 6}个短视频选题，要求：
      - 输出的内容是json数据，不要输出md格式
      - 输出内容不要带 \`\`\`json \`\`\`,
      - 输出必须数据是json数据， 例如[{'order_cn':'','title':'',because: ''}]
      - 每个选题不超过100字
      - 每个选题都要输出推荐理由，输出的推荐理由不超过150个字
   
      输入内容：[${content.prompt}]
    `;
};
export default SelectTitle;
// - 风格轻松有趣
// - 包含不同角度（文化、美食、风景等）
