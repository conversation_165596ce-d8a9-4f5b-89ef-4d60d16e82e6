import { FastifyReply, FastifyRequest } from "fastify";
import WebSocket from "ws";
import { formatSSE, formatWSS, LLMStream } from "../common";
import { selectTitle, writerE<PERSON><PERSON>, RefineCopy } from "../prompt";
import { deepseekConfig } from "../config";
import { getArticleAll, insertArticleOne } from "../../models/article";

interface GetDeepSeekStreamProps {
  params: { [key: string]: any };
  reply?: FastifyReply;
  wss?: boolean;
  socket?: WebSocket.WebSocket;
}
/** 选题
 * @description: 选题
 * @param {} params
 * @param {FastifyReply} reply
 */
export const GetDeepSeekStreamSelectTitle = async ({
  reply,
  wss,
  socket,
  params,
}: GetDeepSeekStreamProps) => {
  // 开始事件
  const startEvent = () => {
    console.log("开始");
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "start",
            timestamp: new Date().toISOString(),
            message: `${inputData}`,
          },
          "start"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "start",
            timestamp: new Date().toISOString(),
            message: `${inputData}`,
          },
          "start"
        )
      );
    }
  };
  // 结束事件
  const endEvent = () => {
    console.log("结束");
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "end",
            timestamp: new Date().toISOString(),
            message: `${JSON.stringify({})}`,
          },
          "end"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "end",
            timestamp: new Date().toISOString(),
            message: `${JSON.stringify({})}`,
          },
          "end"
        )
      );
    }
  };
  // 流数据监听
  const messageEvent = ({ message }: { [key: string]: string }) => {
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "message",
            timestamp: new Date().toISOString(),
            message: message,
          },
          "message"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "message",
            timestamp: new Date().toISOString(),
            message: message,
          },
          "message"
        )
      );
    }
  };
  // 错误事件
  const errorEvent = ({ state, message }: any) => {
    console.log("错误", state, message);
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "error",
            timestamp: new Date().toISOString(),
            message: `${state}:${message}`,
          },
          "error"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "error",
            timestamp: new Date().toISOString(),
            message: `${state}:${message}`,
          },
          "error"
        )
      );
    }
  };
  // 请求完成
  const finishEvent = async ({ value }: { [key: string]: any }) => {
    console.log("请求完成");
    try {
      const data = await insertArticleOne({
        open_id: params.open_id,
        input_value: params.input_value,
        params: params.params,
        LLM_result: params.LLM_result,
        parse_result: params.parse_result,
        thinking: params.thinking,
        type: params.type || "article",
        create_at: new Date().toISOString(),
        update_at: new Date().toISOString(),
        delete_at: null,
        token: params.token || 10,
      });
      if (wss && socket) {
        socket.send(
          formatWSS(
            {
              status: "finish",
              timestamp: new Date().toISOString(),
              message: `${JSON.stringify({})}`,
            },
            "finish"
          )
        );
      } else if (reply) {
        reply.raw.write(
          formatSSE(
            {
              status: "finish",
              timestamp: new Date().toISOString(),
              message: `${JSON.stringify({})}`,
            },
            "finish"
          )
        );
      }
    } catch (error: any) {
      if (wss && socket) {
        socket.send(
          formatWSS(
            {
              status: "error",
              timestamp: new Date().toISOString(),
              message: error.message,
            },
            "finish"
          )
        );
      } else if (reply) {
        reply.raw.write(
          formatSSE(
            {
              status: "error",
              timestamp: new Date().toISOString(),
              message: error.message,
            },
            "finish"
          )
        );
      }
    }
  };

  // prompt 转成字符串
  const inputData = selectTitle({ content: params });
  // 处理用户输入的内容
  console.log("prompt:", params, JSON.stringify(inputData));
  // 配置信息
  const { DEEPSEEK_API_KEY, DEEPSEEK_API_URL } = deepseekConfig;
  const { stop, output } = await LLMStream({
    url: DEEPSEEK_API_URL,
    keys: DEEPSEEK_API_KEY,
    body: {
      model: "deepseek-chat",
      stream: true,
      messages: [{ role: "user", content: JSON.stringify(inputData) }],
    },
    start: startEvent,
    end: endEvent,
    message: messageEvent,
    error: errorEvent,
    finish: finishEvent,
  });
};
/**
 * 创建文案
 * @param {string} prompt
 * @param {FastifyReply} reply
 * @returns {Promise<void>}
 */
export const GetDeepSeekStreamWriterExpert = async ({
  params,
  reply,
  wss,
  socket,
}: GetDeepSeekStreamProps) => {
  // 开始事件
  const startEvent = () => {
    console.log("开始");
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "start",
            timestamp: new Date().toISOString(),
            message: `${inputData}`,
          },
          "start"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "start",
            timestamp: new Date().toISOString(),
            message: `${inputData}`,
          },
          "start"
        )
      );
    }
  };
  // 结束事件
  const endEvent = () => {
    console.log("结束");
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "end",
            timestamp: new Date().toISOString(),
            message: `${JSON.stringify({})}`,
          },
          "end"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "end",
            timestamp: new Date().toISOString(),
            message: `${JSON.stringify({})}`,
          },
          "end"
        )
      );
    }
  };
  // 流数据监听
  const messageEvent = ({ message }: { [key: string]: string }) => {
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "message",
            timestamp: new Date().toISOString(),
            message: message,
          },
          "message"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "message",
            timestamp: new Date().toISOString(),
            message: message,
          },
          "message"
        )
      );
    }
  };
  // 错误事件
  const errorEvent = ({ state, message }: any) => {
    console.log("错误", state, message);
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "error",
            timestamp: new Date().toISOString(),
            message: `${state}:${message}`,
          },
          "error"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "error",
            timestamp: new Date().toISOString(),
            message: `${state}:${message}`,
          },
          "error"
        )
      );
    }
  };
  // 请求完成
  const finishEvent = async ({ value }: { [key: string]: any }) => {
    console.log("请求完成");
    try {
      const data = await insertArticleOne({
        open_id: params.open_id,
        input_value: params.input_value,
        params: params.params,
        LLM_result: params.LLM_result,
        parse_result: params.parse_result,
        thinking: params.thinking,
        type: params.type || "article",
        token: params.token || 0,
        create_at: new Date().toISOString(),
        update_at: new Date().toISOString(),
        delete_at: null,
      });
      console.log("插入成功", data.id);
      if (wss && socket) {
        socket.send(
          formatWSS(
            {
              status: "finish",
              timestamp: new Date().toISOString(),
              message: `${JSON.stringify({})}`,
            },
            "finish"
          )
        );
      } else if (reply) {
        reply.raw.write(
          formatSSE(
            {
              status: "finish",
              timestamp: new Date().toISOString(),
              message: `${JSON.stringify({})}`,
            },
            "finish"
          )
        );
      }
    } catch (error) {
      console.log("插入失败", error);
      if (wss && socket) {
        socket.send(
          formatWSS(
            {
              status: "finish",
              timestamp: new Date().toISOString(),
              message: `${JSON.stringify({})}`,
            },
            "finish"
          )
        );
      } else if (reply) {
        reply.raw.write(
          formatSSE(
            {
              status: "finish",
              timestamp: new Date().toISOString(),
              message: `${JSON.stringify({})}`,
            },
            "finish"
          )
        );
      }
    }
  };
  // prompt 转成字符串
  const inputData = writerExpert({ content: params.prompt });
  // 配置信息
  const { DEEPSEEK_API_KEY, DEEPSEEK_API_URL } = deepseekConfig;
  const { stop, output } = await LLMStream({
    url: DEEPSEEK_API_URL,
    keys: DEEPSEEK_API_KEY,
    body: {
      model: "deepseek-chat",
      stream: true,
      messages: [{ role: "user", content: JSON.stringify(inputData) }],
    },
    start: startEvent,
    end: endEvent,
    message: messageEvent,
    error: errorEvent,
    finish: finishEvent,
  });
};

/**
 * 润色文案
 * @param {string} prompt
 * @param {FastifyReply} reply
 * @returns {Promise<void>}
 */
export const GetDeepSeekStreamModifArticle = async ({
  params,
  // openId,
  // write,
  reply,
  wss,
  socket,
}: {
  params: { [key: string]: any };
  // openId: string;
  // write: string;
  reply?: FastifyReply;
  wss?: boolean;
  socket?: WebSocket.WebSocket;
}) => {
  // 开始事件
  const startEvent = () => {
    console.log("开始");
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "start",
            timestamp: new Date().toISOString(),
            message: `{}`,
          },
          "start"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "start",
            timestamp: new Date().toISOString(),
            message: `{}`,
          },
          "start"
        )
      );
    }
  };
  // 结束事件
  const endEvent = () => {
    console.log("结束");
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "end",
            timestamp: new Date().toISOString(),
            message: `${JSON.stringify({})}`,
          },
          "end"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "end",
            timestamp: new Date().toISOString(),
            message: `${JSON.stringify({})}`,
          },
          "end"
        )
      );
    }
  };
  // 流数据监听
  const messageEvent = ({ message }: { [key: string]: string }) => {
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "message",
            timestamp: new Date().toISOString(),
            message: message,
          },
          "message"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "message",
            timestamp: new Date().toISOString(),
            message: message,
          },
          "message"
        )
      );
    }
  };
  // 错误事件
  const errorEvent = ({ state, message }: any) => {
    console.log("错误", state, message);
    if (wss && socket) {
      socket.send(
        formatWSS(
          {
            status: "error",
            timestamp: new Date().toISOString(),
            message: `${state}:${message}`,
          },
          "error"
        )
      );
    } else if (reply) {
      reply.raw.write(
        formatSSE(
          {
            status: "error",
            timestamp: new Date().toISOString(),
            message: `${state}:${message}`,
          },
          "error"
        )
      );
    }
  };
  // 请求完成
  const finishEvent = async ({
    value,
    thinking,
    text,
  }: {
    [key: string]: any;
  }) => {
    console.log("请求完成");
    try {
      const data = await insertArticleOne({
        open_id: params.openId,
        input_value: params.write,
        params: params,
        LLM_result: value,
        parse_result: params.parse_result,
        thinking: thinking,
        type: "article",
        token: params.token || 0,
        create_at: new Date().toISOString(),
        update_at: new Date().toISOString(),
        delete_at: null,
        // openId: openId,
        // token: 10,
        // content: value,
        // thinking: thinking,
        // text: text,
        // inputValue: write,
        // status: 1,
        // type: "writingModify",
        // create_at: new Date().toISOString(),
        // update_at: new Date().toISOString(),
        // delete_at: "",
      });
      console.log("插入成功", data.id);
      if (wss && socket) {
        socket.send(
          formatWSS(
            {
              status: "finish",
              timestamp: new Date().toISOString(),
              message: `${JSON.stringify({})}`,
            },
            "finish"
          )
        );
      } else if (reply) {
        reply.raw.write(
          formatSSE(
            {
              status: "finish",
              timestamp: new Date().toISOString(),
              message: `${JSON.stringify({})}`,
            },
            "finish"
          )
        );
      }
    } catch (error) {
      console.log("插入失败", error);
      if (wss && socket) {
        socket.send(
          formatWSS(
            {
              status: "finish",
              timestamp: new Date().toISOString(),
              message: `${JSON.stringify({})}`,
            },
            "finish"
          )
        );
      } else if (reply) {
        reply.raw.write(
          formatSSE(
            {
              status: "finish",
              timestamp: new Date().toISOString(),
              message: `${JSON.stringify({})}`,
            },
            "finish"
          )
        );
      }
    }
  };
  // prompt 转成字符串
  const prompt = RefineCopy({ content: params.write });
  console.log("prompt:", prompt);
  // 配置信息
  const { DEEPSEEK_API_KEY, DEEPSEEK_API_URL } = deepseekConfig;
  const { stop, output } = await LLMStream({
    url: DEEPSEEK_API_URL,
    keys: DEEPSEEK_API_KEY,
    body: {
      model: "deepseek-chat",
      stream: true,
      messages: [{ role: "user", content: prompt }],
    },
    start: startEvent,
    end: endEvent,
    message: messageEvent,
    error: errorEvent,
    finish: finishEvent,
  });
};
