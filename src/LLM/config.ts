import path from "path";
import configObj from "../globalConfig";
export const deepseekConfig = {
  DEEPSEEK_API_KEY: "***********************************",
  DEEPSEEK_API_URL: "https://api.deepseek.com/chat/completions",
};
export const AudioConfig = {
  uploadDir: path.join(configObj.systemConfig.rootPath, "/audio"),
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedMimeTypes: [
    "audio/mpeg", // MP3
    "audio/wav", // WAV
    "audio/ogg", // OGG
    "audio/flac", // FLAC
    "audio/webm", // WebM
    "audio/opus", // Opus
    "audio/x-m4a",
    "audio/mp4",
  ],
};
export const VideoConfig = {
  uploadDir: path.join(configObj.systemConfig.rootPath, "/video"),
  maxFileSize: 100 * 1024 * 1024, // 10MB
  allowedMimeTypes: ["video/mp4", "video/webm", "video/ogg"],
};

export const CloneAudioConfig = {
  uploadDir: path.join(configObj.systemConfig.rootPath, "/cloneAudio"),
  allowedMimeTypes: ["audio/mpeg", "audio/wav", "audio/ogg", "audio/flac"],
  fileType: "mp3",
};
