import { v4 as uuidv4 } from "uuid";
import globalObj from "../globalConfig";
import { ArticleTable } from "@/types/table";
import dayjs from "dayjs";

const timeFormatter = () => {
  return dayjs().format("YYYY-MM-DD HH:mm:ss");
};
/** 新增数据 */
export async function insertArticleOne(params: ArticleTable) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("article_table");
  const uuid = params.uuid || uuidv4();
  const result = await db?.insertOne({
    uuid: uuid,
    open_id: params.open_id,
    input_value: params.input_value,
    params: params.params,
    LLM_result: params.LLM_result,
    parse_result: params.parse_result,
    thinking: params.thinking,
    type: params.type || "article",
    token: params.token || 0,
    create_at: timeFormatter(),
    update_at: timeFormatter(),
    delete_at: null,
  });
  return { id: result?.insertedId, uuid: uuid };
}

interface GetArticleParams {
  uuid?: string;
  open_id?: string;
  type?: "selectTopic" | "article" | "polish";
}
/** 查询所有文章数据 */
export async function getArticleAll(params = {} as GetArticleParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("article_table");
  const result = await db?.find(params).toArray();
  return result;
}
/** 获取单独一个文章数据 */
export async function getArticleOne(params: GetArticleParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("article_table");
  const result = await db?.findOne(params);
  return result;
}

/** 更新信息 */
export async function updateArticleOne(
  params: Partial<ArticleTable> & { uuid: string }
) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("article_table");
  const result = await db?.updateOne(
    { uuid: params.uuid },
    {
      $set: {
        ...params,
        update_at: timeFormatter(),
      },
    }
  );

  if (result?.matchedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  return { code: 200, message: "更新成功" };
}
/** 删除数据 */
export async function deleteArticleOne({ uuid }: { uuid: string }) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("article_table");
  const result = await db?.deleteOne({ uuid: uuid });

  if (result?.deletedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  return { code: 200, message: "删除成功" };
}
/** 查询数据总数 */
export async function countArticles(params = {} as GetArticleParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("article_table");
  const count = await db?.countDocuments(params);
  return { count };
}
/** 分页面查询数据 */
export async function getArticlePagination(
  params = {} as GetArticleParams & { page?: number; pageSize?: number }
) {
  const page = params.page || 1;
  const pageSize = params.pageSize || 10;
  const skip = (page - 1) * pageSize;

  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("article_table");

  // Remove pagination params from the query
  const query = { ...params };
  delete query.page;
  delete query.pageSize;

  const total = await db?.countDocuments(query);
  const result = await db
    ?.find(query)
    .sort({ create_at: -1 })
    .skip(skip)
    .limit(pageSize)
    .toArray();

  return {
    data: result,
    pagination: {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total ?? 0 / pageSize),
    },
  };
}
export default {
  insertArticleOne,
  getArticleAll,
  getArticleOne,
  updateArticleOne,
  deleteArticleOne,
  countArticles,
  getArticlePagination,
};
