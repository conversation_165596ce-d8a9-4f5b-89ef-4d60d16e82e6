import { systemConfigTable } from "@/types/table";
import globalObj from "../globalConfig";
import dayjs from "dayjs";
import { ObjectId } from "mongodb";

const timeFormatter = () => {
  return dayjs().format("YYYY-MM-DD HH:mm:ss");
};

/** 插入系统配置数据 */
export async function insertSystemConfigDB(
  params: Omit<systemConfigTable, "_id">
) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("system_config_table");

  const result = await db?.insertOne({
    select_topic_number: params.select_topic_number,
    prompt_template: params.prompt_template,
    create_at: timeFormatter(),
    update_at: timeFormatter(),
    delete_at: null,
  });

  return { id: result?.insertedId };
}

/** 查询系统配置数据 */
export async function getSystemConfigDB(
  params: { _id?: string; prompt_template?: boolean } = {}
) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("system_config_table");

  const query: any = { delete_at: null };

  if (params._id) {
    try {
      query._id = new ObjectId(params._id);
    } catch (error) {
      console.error("Invalid ObjectId format:", error);
      return [];
    }
  }

  // 如果只查询 prompt_template 数据
  if (params.prompt_template) {
    const result = await db
      ?.find(query, { projection: { prompt_template: 1, _id: 1 } })
      .toArray();
    return result;
  }

  const result = await db?.find(query).toArray();
  return result;
}

/** 更新系统配置数据 */
export async function updateSystemConfigDB(
  filter: { _id: string },
  update: Partial<Omit<systemConfigTable, "_id">>
) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("system_config_table");

  // 确保至少有一个更新字段
  if (Object.keys(update).length === 0) {
    return { code: 400, message: "参数错误，至少需要一个更新字段" };
  }

  let objectId;
  try {
    objectId = new ObjectId(filter._id);
  } catch (error) {
    console.error("Invalid ObjectId format:", error);
    return { code: 400, message: "无效的ID格式" };
  }

  // 执行更新操作
  const result = await db?.updateOne(
    { _id: objectId },
    {
      $set: {
        ...update,
        update_at: timeFormatter(),
      },
    }
  );

  if (result?.matchedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  if (result?.modifiedCount === 0) {
    return { code: 200, message: "数据未发生变化" };
  }

  return { code: 200, message: "更新成功" };
}

/** 删除系统配置数据（软删除） */
export async function deleteSystemConfigDB(filter: { _id: string }) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("system_config_table");

  let objectId;
  try {
    objectId = new ObjectId(filter._id);
  } catch (error) {
    console.error("Invalid ObjectId format:", error);
    return { code: 400, message: "无效的ID格式" };
  }

  const result = await db?.updateOne(
    { _id: objectId },
    {
      $set: {
        delete_at: timeFormatter(),
        update_at: timeFormatter(),
      },
    }
  );

  if (result?.matchedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  return { code: 200, message: "删除成功" };
}
