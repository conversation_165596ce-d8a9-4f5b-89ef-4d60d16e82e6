import { v4 as uuidv4 } from "uuid";
import globalObj from "../globalConfig";
import { WorkflowTable } from "../types/table";
import dayjs from "dayjs";

const timeFormatter = () => {
  return dayjs().format("YYYY-MM-DD HH:mm:ss");
};
/** 插入创作流数据 */
export async function insertWorkflowOne(params: WorkflowTable) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("workflow_table");
  const uuid = params.uuid || uuidv4();
  const result = await db?.insertOne({
    uuid: uuid,
    open_id: params.open_id,
    type: params.type,
    result: params.result,
<<<<<<< HEAD
    create_at: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    update_at: dayjs().format("YYYY-MM-DD HH:mm:ss"),
=======
    create_at: timeFormatter(),
    update_at: timeFormatter(),
>>>>>>> 0dbb5af46c4af1bccb080b6d542cd46e68b08449
    delete_at: null,
  });
  return { id: result?.insertedId, uuid: uuid };
}

/** 更新创作流参数接口 */
export interface UpdateWorkflowParams {
  uuid: string;
  type?: "selectTopic" | "article";
  result?: {
    selectTitle?: any;
    createwriting?: any;
    selectAudio?: any;
    createAudio?: any;
    selectvideo?: any;
    createVideo?: any;
  };
}

/** 更新创作流信息 */
export async function updateWorkflowOne(params: UpdateWorkflowParams) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("workflow_table");
  const result = await db?.updateOne(
    { uuid: params.uuid, delete_at: null },
    {
      $set: {
        ...params,
        update_at: timeFormatter(),
      },
    }
  );
  if (result?.matchedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }
  return { code: 200, message: "更新成功" };
}

/** 查询创作流参数接口 */
export interface GetWorkflowParams {
  uuid?: string;
  open_id?: string;
  process?: number;
  type?: "selectTopic" | "article";
  page?: number;
  pageSize?: number;
}

/** 获取所有创作流信息 */
export async function getWorkflowAll(params = {} as GetWorkflowParams) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("workflow_table");

  // 构建查询条件
  const query: any = { delete_at: null };

  if (params.uuid) query.uuid = params.uuid;
  if (params.open_id) query.open_id = params.open_id;
  if (params.type) query.type = params.type;

  const result = await db?.find(query).toArray();
  return result;
}

/** 分页查询创作流信息 */
export async function getWorkflowPagination(params = {} as GetWorkflowParams) {
  const page = params.page || 1;
  const pageSize = params.pageSize || 10;
  const skip = (page - 1) * pageSize;

  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("workflow_table");

  // 构建查询条件
  const query: any = { delete_at: null };

  if (params.uuid) query.uuid = params.uuid;
  if (params.open_id) query.open_id = params.open_id;
  if (params.type) query.type = params.type;

  // 移除分页参数
  const queryParams = { ...params };
  delete queryParams.page;
  delete queryParams.pageSize;

  const total = await db?.countDocuments(query);
  const result = await db
    ?.find(query)
    .sort({ create_at: -1 })
    .skip(skip)
    .limit(pageSize)
    .toArray();

  return {
    data: result,
    pagination: {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total ?? 0 / pageSize),
    },
  };
}

/** 根据ID获取单个创作流信息 */
export async function getWorkflowById(uuid: string) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("workflow_table");

  const result = await db?.findOne({ uuid: uuid, delete_at: null });

  if (!result) {
    return { code: 404, message: "没有找到数据" };
  }

  return result;
}

/** 删除创作流信息（软删除） */
export async function deleteWorkflowOne(uuid: string) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("workflow_table");

  const result = await db?.updateOne(
    { uuid: uuid, delete_at: null },
    {
      $set: {
        delete_at: timeFormatter(),
        update_at: timeFormatter(),
      },
    }
  );

  if (result?.matchedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  return { code: 200, message: "删除成功" };
}

/** 物理删除创作流信息（谨慎使用） */
export async function permanentDeleteWorkflow(uuid: string) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("workflow_table");

  const result = await db?.deleteOne({ uuid: uuid });

  if (result?.deletedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  return { code: 200, message: "永久删除成功" };
}

/** 查询创作流数据总数 */
export async function countWorkflows(params = {} as GetWorkflowParams) {
  const db = globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("workflow_table");

  // 构建查询条件
  const query: any = { delete_at: null };

  if (params.uuid) query.uuid = params.uuid;
  if (params.open_id) query.open_id = params.open_id;
  if (params.type) query.type = params.type;

  const count = await db?.countDocuments(query);
  return { count };
}

export default {
  insertWorkflowOne,
  updateWorkflowOne,
  getWorkflowAll,
  getWorkflowPagination,
  getWorkflowById,
  deleteWorkflowOne,
  permanentDeleteWorkflow,
  countWorkflows,
};
