import { v4 as uuidv4 } from "uuid";
import globalObj from "../globalConfig";
import { IPSettingTable } from "@/types/table";
import dayjs from "dayjs";

const timeFormatter = () => {
  return dayjs().format("YYYY-MM-DD HH:mm:ss");
};

/** 插入IP人设信息 */
export async function insertIPSettingDB(params: IPSettingTable) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("ip_table");
  const uuid = params.uuid || uuidv4();
  const result = await db?.insertOne({
    uuid: uuid,
    open_id: params.open_id,
    ip_name: params.ip_name,
    sex: params.sex,
    create_type: params.create_type,
    target_audience: params.target_audience,
    key_information: params.key_information,
    track_position: params.track_position,
    create_at: timeFormatter(),
    update_at: timeFormatter(),
    delete_at: null,
  });
  return { id: result?.insertedId, uuid: uuid };
}

interface GetIPSettingParams {
  uuid?: string;
  open_id?: string;
  ip?: string;
  default?: boolean;
}
/** 查询所有的数据 */
export async function getIPSettingAll(params = {} as GetIPSettingParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("ip_table");
  const result = await db?.find(params).toArray();
  return result;
}
/** 查询一条数据 */
export async function getIPSettingOne(params: GetIPSettingParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("ip_table");
  const result = await db?.findOne(params);
  return result;
}

/**
 * 删除数据
 * @param
 *  uuid: string;
 *  open_id?: string;
 *
 * */
export async function deleteIPSettingDB({
  uuid,
  open_id,
}: {
  uuid: string;
  open_id?: string;
}) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("ip_table");

  const query: { uuid: string; open_id?: string } = { uuid };
  if (open_id) query.open_id = open_id;

  const result = await db?.deleteOne(query);

  if (result?.deletedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }
  return { code: 200, message: "删除成功" };
}
/**
 * 获取数据数量
 */
export async function countIPSettings(params = {} as GetIPSettingParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("ip_table");
  const count = await db?.countDocuments(params);
  return { count };
}
/**
 * 分页查询数据
 */
export async function getIPSettingPagination(
  params = {} as GetIPSettingParams & { page?: number; pageSize?: number }
) {
  const page = params.page || 1;
  const pageSize = params.pageSize || 10;
  const skip = (page - 1) * pageSize;

  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("ip_table");

  // Remove pagination params from the query
  const query = { ...params };
  delete query.page;
  delete query.pageSize;

  const total = await db?.countDocuments(query);
  const result = await db
    ?.find(query)
    .sort({ create_at: -1 })
    .skip(skip)
    .limit(pageSize)
    .toArray();
  return {
    data: result,
    pagination: {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total ?? 0 / pageSize),
    },
  };
}
/** 更新数据 */
export async function updateUserDB(
  filter: { uuid: string; open_id?: string },
  update: Partial<
    Omit<
      IPSettingTable,
      "uuid" | "open_id" | "create_at" | "update_at" | "delete_at"
    >
  >
) {
  console.log("*****", filter, update);
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("ip_table");

  // 确保至少有一个更新字段
  if (Object.keys(update).length === 0) {
    return { code: 400, message: "参数错误，至少需要一个更新字段" };
  }

  // 执行更新操作
  const result = await db?.updateOne(filter, {
    $set: {
      ...update,
      update_at: timeFormatter(),
    },
  });

  if (result?.matchedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  if (result?.modifiedCount === 0) {
    return { code: 200, message: "数据未发生变化" };
  }

  return { code: 200, message: "更新成功" };
}
export default {
  updateUserDB,
  insertIPSettingDB,
  getIPSettingAll,
  getIPSettingOne,
  deleteIPSettingDB,
  countIPSettings,
  getIPSettingPagination,
};
