import ArticleDB from "./article";
import UserDB from "./user";
import VideoDB from "./video";
import AudioDB from "./audio";
import IPSettingDB from "./ipSetting";
import WorkflowDB from "./workflow";
import { FastifyInstance } from "fastify";
import { v4 as uuidv4 } from "uuid";
import dayjs from "dayjs";
export { ArticleDB, UserDB, VideoDB, AudioDB, IPSettingDB, WorkflowDB };

const timeFormatter = () => {
  return dayjs().format("YYYY-MM-DD HH:mm:ss");
};
/** 创建IP人设表 */
export const InitIPSettingDB = async (fastify: FastifyInstance) => {
  const usersCollection = await fastify.mongo?.db?.createCollection("ip_table"); // 获取数据库中的users集合
  const result = await usersCollection?.insertOne({
    uuid: uuidv4(),
    open_id: "admin",
    ip_name: "电商主播",
    sex: "man",
    create_type: "11",
    target_audience: "卖货",
    key_information: "飞机大炮",
    track_position: "武器",
    create_at: timeFormatter(),
    update_at: timeFormatter(),
    delete_at: null,
  });
  return { id: result?.insertedId };
};
/** 创建作品表 */
export const InitArticleDB = async (fastify: FastifyInstance) => {
  const usersCollection = await fastify.mongo?.db?.createCollection(
    "article_table"
  ); // 获取数据库中的users集合
  const result = await usersCollection?.insertOne({
    uuid: uuidv4(),
    content: "测试",
    status: 0,
    create_at: timeFormatter(),
    update_at: timeFormatter(),
    delete_at: timeFormatter(),
  });
  return { id: result?.insertedId };
};
/** 创建用户信息表 */
export const InitUserDB = async (fastify: FastifyInstance) => {
  const usersCollection = await fastify.mongo?.db?.createCollection(
    "user_table"
  ); // 获取数据库中的users集合
  const result = await usersCollection?.insertOne({
    uuid: uuidv4(),
    name: "测试",
    password: "测试",
    model: "测试",
    role: "测试",
    tokens: 1000,
  });
  return { id: result?.insertedId };
};
/** 创建视频表 */
export const InitVideoDB = async (fastify: FastifyInstance) => {
  const usersCollection = await fastify.mongo?.db?.createCollection(
    "video_table"
  ); // 获取数据库中的users集合
  const result = await usersCollection?.insertOne({
    uuid: uuidv4(),
    title: "测试视频",
    description: "测试视频描述",
    file_url: "测试视频链接",
    file_size: "测试视频大小",
    duration: 100,
    format: "测试视频格式",
    status: "测试视频状态",
    cover_image: "",
    create_at: timeFormatter(),
    update_at: timeFormatter(),
    delete_at: timeFormatter(),
  });
  return { id: result?.insertedId };
};
/** 创建音频表 */
export const InitAudioDB = async (fastify: FastifyInstance) => {
  const usersCollection = await fastify.mongo?.db?.createCollection(
    "audio_table"
  ); // 获取数据库中的users集合
  const result = await usersCollection?.insertOne({
    uuid: uuidv4(),
    title: "测试音频",
    description: "测试音频描述",
    file_url: "测试音频链接",
    file_size: "测试音频大小",
    duration: 100,
    format: "测试音频格式",
    status: "测试音频状态",
    cover_image: "",
    create_at: timeFormatter(),
    update_at: timeFormatter(),
    delete_at: timeFormatter(),
  });
  return { id: result?.insertedId };
};
/** 创建创作流表 */
export const InitWorkflowDB = async (fastify: FastifyInstance) => {
  const workflowCollection = await fastify.mongo?.db?.createCollection(
    "workflow_table"
  ); // 获取数据库中的workflow集合
  const result = await workflowCollection?.insertOne({
    uuid: uuidv4(),
    open_id: "test_open_id",
    type: "article",
    result: {
      createwriting: {
        state: true,
        data: "测试创作流内容",
        stream: "",
        editor: false,
      },
    },
    create_at: timeFormatter(),
    update_at: timeFormatter(),
    delete_at: null,
  });
  return { id: result?.insertedId };
};
