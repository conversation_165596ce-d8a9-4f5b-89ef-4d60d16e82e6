import globalObj from "../globalConfig";
import { v4 as uuidv4 } from "uuid";
import { AudioTable } from "@/types/table";
import dayjs from "dayjs";

const timeFormatter = () => {
  return dayjs().format("YYYY-MM-DD HH:mm:ss");
};

/** 插入音频数据 */
export async function insertAudioOne(params: AudioTable) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("audio_table");
  const uuid = params.uuid || uuidv4();
  const result = await db?.insertOne({
    uuid: uuid,
    open_id: params.open_id,
    title: params.title,
    description: params.description || "",
    oss_url: params.oss_url,
    oss_path: params.oss_path,
    format: params.format,
    duration: params.duration,
    model_id: params.model_id,
    model_status: params.model_status,
    create_at: timeFormatter(),
    update_at: timeFormatter(),
    delete_at: null,
  });
  return { id: result?.insertedId, uuid: uuid };
}

interface GetAudioParams {
  uuid?: string;
  open_id?: string;
  model_id?: string;
}

/** 查询所有音频数据 */
export async function getAudioAll(params = {} as GetAudioParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("audio_table");
  const result = await db?.find(params).toArray();
  return result;
}

/** 查询单个音频数据 */
export async function getAudioOne(params: GetAudioParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("audio_table");
  const result = await db?.findOne(params);
  return result;
}

/** 更新音频数据 */
export async function updateAudioOne(
  params: Partial<AudioTable> & { uuid: string }
) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("audio_table");
  const result = await db?.updateOne(
    { uuid: params.uuid },
    {
      $set: {
        ...params,
        update_at: timeFormatter(),
      },
    }
  );

  if (result?.matchedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  return { code: 200, message: "更新成功" };
}

/** 删除音频数据 */
export async function deleteAudioOne({
  uuid,
  open_id,
}: {
  uuid: string;
  open_id?: string;
}) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("audio_table");

  const query: { uuid: string; open_id?: string } = { uuid };
  if (open_id) query.open_id = open_id;

  const result = await db?.deleteOne(query);

  if (result?.deletedCount === 0) {
    return { code: 404, message: "没有找到数据" };
  }

  return { code: 200, message: "删除成功" };
}

/** 查询音频数据总数 */
export async function countAudios(params = {} as GetAudioParams) {
  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("audio_table");
  const count = await db?.countDocuments(params);
  return { count };
}

/** 分页查询音频数据 */
export async function getAudioPagination(
  params = {} as GetAudioParams & { page?: number; pageSize?: number }
) {
  const page = params.page || 1;
  const pageSize = params.pageSize || 10;
  const skip = (page - 1) * pageSize;

  const db = await globalObj
    .getFastifyInstance()
    .mongo?.db?.collection("audio_table");

  // Remove pagination params from the query
  const query = { ...params };
  delete query.page;
  delete query.pageSize;

  const total = await db?.countDocuments(query);
  const result = await db
    ?.find(query)
    .sort({ create_at: -1 })
    .skip(skip)
    .limit(pageSize)
    .toArray();

  return {
    list: result,
    pagination: {
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total ?? 0 / pageSize),
    },
  };
}

export default {
  insertAudioOne,
  getAudioAll,
  getAudioOne,
  updateAudioOne,
  deleteAudioOne,
  countAudios,
  getAudioPagination,
};
