import Fastify from "fastify";
import fastifyMultipart from "@fastify/multipart";
import { setupMongoDB } from "./db/db";
import GlobalObj from "./globalConfig";
import FastifyWebsocket from "@fastify/websocket";
import { formatValidationError } from "./utils/errorFormatter";
import Swagger from "@fastify/swagger";
import path from "path";
import fs from "fs";
import fjwt from "@fastify/jwt";
import staticFiles from "@fastify/static";
import { AudioConfig, VideoConfig } from "./LLM/config";

// import { setupMongoDB } from "./db/db";
import fastifyCors from "@fastify/cors";
import router from "./routes";

const fastify = Fastify({
  genReqId: (req) => crypto.randomUUID(),
  connectionTimeout: 100000, // 5秒连接超时
  requestTimeout: 200000, // 10秒请求超时
  logger: {
    level: "info",
    // transport: {
    //   target: "pino-pretty", // 开发环境美化输出
    //   options: { colorize: true },
    // },
    serializers: {
      req(req) {
        return {
          method: req.method,
          url: req.url,
          headers: req.headers,
        };
      },
    },
  },

  // https: {
  //   key: fs.readFileSync(path.resolve(__dirname, "../ssl", "ssl.key")),
  //   cert: fs.readFileSync(path.resolve(__dirname, "../ssl", "ssl.pem")),
  // },
});

// 注册错误处理钩子
fastify.setErrorHandler(formatValidationError);

// 为每个请求添加唯一 ID
fastify.addHook("onRequest", (request, reply, done) => {
  request.id = crypto.randomUUID(); // 可以使用uuid或其它方法生成
  done();
});
// 使用 Pino 的 child logger
fastify.addHook("preHandler", (request, reply, done) => {
  request.log = fastify.log.child({ reqId: request.id });
  done();
});

// 注册插件
async function setupPlugins() {
  // 文件上传插件
  await fastify.register(fastifyMultipart, {
    limits: {
      fileSize: 100 * 1024 * 1024,
      files: 3,
    },
  });
}

async function main() {
  // 初始化数据库
  await setupMongoDB(fastify);
  GlobalObj.setFastifyInstance(fastify);
  // await ensureUploadDir();
  await setupPlugins();
  // 注册ws传输方式
  await fastify.register(FastifyWebsocket, {
    options: {
      maxPayload: 1048576,
    },
  });
  // 注册 Swaagger 插件
  fastify.register(Swagger, {
    swagger: {
      info: {
        title: "api 文档",
        description: "展示接口",
        version: "0.1.0",
      },
      externalDocs: {
        url: "https://swagger.io",
        description: "Find more info here",
      },
    },
  });
  fastify.register(fastifyCors, {
    origin: "*", // 或使用 '*' 允许所有域名
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"], // 必须包含OPTIONS
    allowedHeaders: [
      "Origin",
      "X-Requested-With",
      "Content-Type",
      "Accept",
      "Authorization",
    ],
    optionsSuccessStatus: 204,
  });
  // 注册路由
  fastify.register(router);

  // 启动服务
  fastify.listen({ port: 80, host: "0.0.0.0" }, (err) => {
    console.log("服务器：", fastify.server.address());
    if (err) {
      fastify.log.error(err);
      process.exit(1);
    }
  });
}
main();
