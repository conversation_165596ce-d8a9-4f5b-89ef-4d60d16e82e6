import { FastifyInstance } from "fastify";

import { userRoutes } from "./z_user/index";
import { ossUserRoutes } from "./z_oss/index";
import { audioRoutes } from "./z_audio/index";
import { videoRoutes } from "./z_video/index";
import { ipSeetingRoutes } from "./z_ip/index";
import { workflowRoutes } from "./z_workflow/index";
import { wssUserRoutes } from "./z_wss/index";


const route = async (fastify: FastifyInstance) => {
  await userRoutes(fastify);
  await wssUserRoutes(fastify);
  await ossUserRoutes(fastify);
  await audioRoutes(fastify);
  await videoRoutes(fastify);
  await ipSeetingRoutes(fastify);
  await workflowRoutes(fastify);
};
export default route;
