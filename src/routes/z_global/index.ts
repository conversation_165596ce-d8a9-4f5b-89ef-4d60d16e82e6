import { FastifyInstance } from "fastify";
import {
  insertSystemConfigDB,
  getSystemConfigDB,
  updateSystemConfigDB,
  deleteSystemConfigDB,
} from "@/models/globalConfig";
import {
  createSchema,
  updateSchema,
  deleteSchema,
  querySchema,
} from "./schema";

export async function globalRoutes(fastify: FastifyInstance) {
  /** 创建系统配置 */
  fastify.post(
    "/ai/global/create",
    { schema: createSchema },
    async (request, reply) => {
      const params: any = request.body;
      try {
        const result = await insertSystemConfigDB(params);
        return reply.send({
          success: true,
          code: 200,
          message: "创建成功",
          data: result,
        });
      } catch (error: any) {
        console.error("创建系统配置失败:", error);
        return reply.code(500).send({
          success: false,
          code: 500,
          message: error.message || "创建系统配置失败",
        });
      }
    }
  );

  /** 查询系统配置 */
  fastify.post(
    "/ai/global/query",
    { schema: querySchema },
    async (request, reply) => {
      const params: any = request.body;
      try {
        const result = await getSystemConfigDB(params);
        return reply.send({
          success: true,
          code: 200,
          message: "查询成功",
          data: result,
        });
      } catch (error: any) {
        console.error("查询系统配置失败:", error);
        return reply.code(500).send({
          success: false,
          code: 500,
          message: error.message || "查询系统配置失败",
        });
      }
    }
  );

  /** 更新系统配置 */
  fastify.put(
    "/ai/global/update",
    { schema: updateSchema },
    async (request, reply) => {
      const params: any = request.body;
      const { _id, ...updateData } = params;

      if (!_id) {
        return reply.code(400).send({
          success: false,
          code: 400,
          message: "缺少必要参数: _id",
        });
      }

      try {
        const result = await updateSystemConfigDB({ _id }, updateData);

        if (result.code === 200) {
          return reply.send({
            success: true,
            code: 200,
            message: result.message,
          });
        } else {
          return reply.code(result.code).send({
            success: false,
            code: result.code,
            message: result.message,
          });
        }
      } catch (error: any) {
        console.error("更新系统配置失败:", error);
        return reply.code(500).send({
          success: false,
          code: 500,
          message: error.message || "更新系统配置失败",
        });
      }
    }
  );

  /** 删除系统配置 */
  fastify.delete(
    "/ai/global/delete",
    { schema: deleteSchema },
    async (request, reply) => {
      const params: any = request.body;

      if (!params._id) {
        return reply.code(400).send({
          success: false,
          code: 400,
          message: "缺少必要参数: _id",
        });
      }

      try {
        const result = await deleteSystemConfigDB({ _id: params._id });

        if (result.code === 200) {
          return reply.send({
            success: true,
            code: 200,
            message: result.message,
          });
        } else {
          return reply.code(result.code).send({
            success: false,
            code: result.code,
            message: result.message,
          });
        }
      } catch (error: any) {
        console.error("删除系统配置失败:", error);
        return reply.code(500).send({
          success: false,
          code: 500,
          message: error.message || "删除系统配置失败",
        });
      }
    }
  );
}
