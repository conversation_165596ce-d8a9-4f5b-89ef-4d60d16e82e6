/** 创建系统配置的请求体验证 */
export const createSchema = {
  body: {
    type: "object",
    required: ["select_topic_number", "prompt_template"],
    properties: {
      select_topic_number: {
        type: "array",
        items: {
          type: "object",
          properties: {
            label: { type: "string" },
            value: { type: "number" },
          },
          required: ["label", "value"],
        },
      },
      prompt_template: {
        type: "array",
        items: {
          type: "object",
        },
      },
    },
  },
};

/** 查询系统配置的请求体验证 */
export const querySchema = {
  body: {
    type: "object",
    properties: {
      _id: { type: "string" },
      prompt_template: { type: "boolean" },
    },
  },
};

/** 更新系统配置的请求体验证 */
export const updateSchema = {
  body: {
    type: "object",
    required: ["_id"],
    properties: {
      _id: { type: "string" },
      select_topic_number: {
        type: "array",
        items: {
          type: "object",
          properties: {
            label: { type: "string" },
            value: { type: "number" },
          },
          required: ["label", "value"],
        },
      },
      prompt_template: {
        type: "array",
        items: {
          type: "object",
        },
      },
    },
  },
};

/** 删除系统配置的请求体验证 */
export const deleteSchema = {
  body: {
    type: "object",
    required: ["_id"],
    properties: {
      _id: { type: "string" },
    },
  },
};
