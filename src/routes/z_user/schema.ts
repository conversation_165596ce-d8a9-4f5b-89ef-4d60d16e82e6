// 初始化表的请求参数
const initSchema = {
  body: {
    type: "object",
    required: ["tableName"],
    properties: {
      tableName: { type: "array", items: { type: "string" } },
    },
  },
};
// 润色文案请求参数
const writingModifySchema = {
  body: {
    type: "object",
    required: ["content"],
    properties: {
      content: { type: "string" },
    },
  },
};
/** 获取用户信息 */
const userInfoSchema = {
  body: {
    type: "object",
    required: ["open_id", "process"],
    properties: {
      open_id: { type: "string" },
      process: { type: "number" },
      page: { type: "number" },
      pageSize: { type: "number" },
    },
  },
};
export { initSchema, writingModifySchema, userInfoSchema };
