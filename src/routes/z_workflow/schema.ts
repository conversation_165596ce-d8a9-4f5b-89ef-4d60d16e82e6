/** 工作流查询 */
const workflowListSchema = {
  body: {
    type: "object",
    required: ["open_id"],
    properties: {
      open_id: { type: "string" },
      page: { type: "number" },
      pageSize: { type: "number" },
      type: { type: "string" },
    },
  },
};
/** 工作流详情 */
const workflowDetailSchema = {
  body: {
    type: "object",
    required: ["id"],
    properties: {
      id: { type: "string" },
    },
  },
};
/** 工作流数据 */
const workflowDataSchema = {
  body: {
    type: "object",
    required: ["type", "taskId", "openId"],
    properties: {
      type: { type: "string" },
    },
  },
};
/**  创建工作流 */
const createWorkflowSchema = {
  body: {
    type: "object",
    required: ["open_id", "type"],
    properties: {
      open_id: { type: "string" },
      type: { type: "string" },
    },
  },
};
/** 继续工作流 */
const ContinueWorkflowSchema = {
  body: {
    type: "object",
    required: ["open_id", "type", "params", "uuid"],
    properties: {
      uuid: { type: "string" },
      open_id: { type: "string" },
      type: { type: "string" },
      params: { type: "object" },
    },
  },
};
export {
  workflowListSchema,
  workflowDetailSchema,
  workflowDataSchema,
  createWorkflowSchema,
  ContinueWorkflowSchema,
};
