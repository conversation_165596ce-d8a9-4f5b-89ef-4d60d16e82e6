const { build } = require("esbuild");
const path = require("path");
const fs = require("fs");

// 清除 dist 目录的函数
function cleanDir(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
  }
  fs.mkdirSync(dirPath, { recursive: true });
}
// 清除并重建 dist 目录
cleanDir(path.resolve(__dirname, "dist"));

const options = {
  entryPoints: [path.resolve(__dirname, "src/app.ts")],
  outfile: path.resolve(__dirname, "dist/app.js"),
  bundle: true,
  platform: "node",
  target: "node14",
  minify: process.env.NODE_ENV === "production",
  sourcemap: process.env.NODE_ENV !== "production",
  external: [
    // 排除不需要打包的Node.js内置模块和npm依赖
    ...require("module").builtinModules,
    // 可以手动添加其他外部依赖
  ],
};

build(options).catch(() => process.exit(1));
