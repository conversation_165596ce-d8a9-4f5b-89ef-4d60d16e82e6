{"name": "ai_server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon -r tsconfig-paths/register src/app.ts", "start": "node dist/app.js", "build": "node esbuild.config.js"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.4.1", "dependencies": {"@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.0.1", "@fastify/jwt": "^9.1.0", "@fastify/mongodb": "^9.0.2", "@fastify/multipart": "^9.0.3", "@fastify/static": "^8.1.1", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.2", "@fastify/websocket": "^11.0.2", "@sinclair/typebox": "^0.34.33", "@types/fluent-ffmpeg": "^2.1.27", "ali-oss": "^6.22.0", "axios": "^1.8.4", "dayjs": "^1.11.13", "fastify": "^5.2.2", "ffmpeg-static": "^5.2.0", "ffprobe-static": "^3.1.0", "form-data": "^4.0.2", "mongodb": "^6.15.0", "pino-pretty": "^13.0.0", "proxy-agent": "^6.5.0", "pump": "^3.0.2", "punycode": "^2.3.1", "tsconfig-paths": "^4.2.0", "typeorm": "^0.3.22", "uuid": "^11.1.0", "ws": "^8.18.2"}, "devDependencies": {"@types/ali-oss": "^6.16.11", "@types/ffprobe-static": "^2.0.3", "@types/form-data": "^2.5.2", "@types/node": "^22.14.0", "@types/pump": "^1.1.3", "@types/ws": "^8.18.1", "babel-loader": "^10.0.0", "esbuild": "^0.25.3", "nodemon": "^3.1.10", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "webpack": "^5.99.6", "webpack-cli": "^6.0.1"}, "pnpm": {"ignoredBuiltDependencies": ["ffmpeg-static"]}}